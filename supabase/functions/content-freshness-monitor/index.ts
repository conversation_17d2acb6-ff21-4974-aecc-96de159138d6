/// <reference lib="deno.ns" />
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { differenceInMonths, differenceInDays, parseISO } from "https://esm.sh/date-fns@2.29.3";
import { Database, BlogPost, TrekPackage } from '../seo-optimizer/types.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ContentAnalysis {
  freshness_score: number;
  update_priority: 'low' | 'medium' | 'high';
  suggested_updates: Array<{
    section: string;
    reason: string;
    suggestion: string;
  }>;
  seasonal_relevance: boolean;
  next_review_date: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY not configured');
    }

    // Get all content to analyze
    const [blogResponse, trekResponse] = await Promise.all([
      supabaseClient.from('blog_posts').select('*'),
      supabaseClient.from('trek_packages').select('*')
    ]);

    if (blogResponse.error) throw blogResponse.error;
    if (trekResponse.error) throw trekResponse.error;

    const blogs = blogResponse.data || [];
    const treks = trekResponse.data || [];

    const now = new Date();
    const updates: Array<Promise<void>> = [];

    // Process blog posts
    for (const post of blogs) {
      const promptText = `
Analyze this travel blog post about Nepal trekking for freshness and seasonal relevance:

Title: ${post.title}
Last Updated: ${post.updated_at}
Content: ${post.content.substring(0, 1500)}... // truncated for brevity
Tags: ${post.tags.join(', ')}

Consider:
1. Content age and relevance
2. Seasonal trekking information
3. Price and logistics accuracy
4. Current travel trends
5. Health and safety information

Return analysis in this JSON format:
{
  "freshness_score": 0.0-1.0,
  "update_priority": "low" | "medium" | "high",
  "suggested_updates": [{
    "section": "section name",
    "reason": "why update is needed",
    "suggestion": "what to update"
  }],
  "seasonal_relevance": boolean,
  "next_review_date": "YYYY-MM-DD"
}`;

      const analysis = await analyzeContent(geminiApiKey, promptText);
      updates.push(storeAnalysis(supabaseClient, 'blog', post.id, analysis));
    }

    // Process trek packages
    for (const trek of treks) {
      const promptText = `
Analyze this Nepal trekking package for freshness and seasonal relevance:

Name: ${trek.name}
Region: ${trek.region}
Description: ${trek.short_description}
Details: ${trek.long_description}
Last Updated: ${trek.updated_at}

Consider:
1. Price accuracy
2. Seasonal conditions
3. Route conditions
4. Safety information
5. Local regulations
6. Tourism trends

Return analysis in this JSON format:
{
  "freshness_score": 0.0-1.0,
  "update_priority": "low" | "medium" | "high",
  "suggested_updates": [{
    "section": "section name",
    "reason": "why update is needed",
    "suggestion": "what to update"
  }],
  "seasonal_relevance": boolean,
  "next_review_date": "YYYY-MM-DD"
}`;

      const analysis = await analyzeContent(geminiApiKey, promptText);
      updates.push(storeAnalysis(supabaseClient, 'trek', trek.id, analysis));
    }

    await Promise.all(updates);

    return new Response(
      JSON.stringify({
        message: 'Content freshness analysis completed',
        processed: blogs.length + treks.length
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Content freshness analysis error:', error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function analyzeContent(apiKey: string, prompt: string): Promise<ContentAnalysis> {
  const response = await fetch(
    `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      })
    }
  );

  if (!response.ok) {
    throw new Error(`Gemini API error: ${await response.text()}`);
  }

  const data = await response.json();
  const analysisText = data.candidates?.[0]?.content?.parts?.[0]?.text;

  if (!analysisText) {
    throw new Error('No analysis received from Gemini');
  }

  const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
  if (!jsonMatch) {
    throw new Error('No JSON found in response');
  }

  return JSON.parse(jsonMatch[0]);
}

async function storeAnalysis(
  client: ReturnType<typeof createClient<Database>>,
  contentType: 'blog' | 'trek',
  contentId: string,
  analysis: ContentAnalysis
): Promise<void> {
  try {
    const { error } = await client
      .from('content_freshness')
      .upsert({
        content_type: contentType,
        content_id: contentId,
        last_updated: new Date().toISOString(),
        freshness_score: analysis.freshness_score,
        update_priority: analysis.update_priority,
        suggested_updates: analysis.suggested_updates,
        seasonal_relevance: analysis.seasonal_relevance,
        next_review_date: analysis.next_review_date
      }, {
        onConflict: 'content_type,content_id'
      });

    if (error) throw error;
  } catch (error) {
    console.error(`Error storing analysis for ${contentType} ${contentId}:`, error);
    throw error;
  }
}
