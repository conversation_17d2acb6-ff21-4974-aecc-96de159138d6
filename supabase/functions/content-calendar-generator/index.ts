/// <reference lib="deno.ns" />
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { addMonths, format, parseISO } from "https://esm.sh/date-fns@2.29.3";
import { Database, ContentCalendar, BlogPost } from '../seo-optimizer/types.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface GeminiResponse {
  candidates?: Array<{
    content?: {
      parts?: Array<{
        text: string;
      }>;
    };
  }>;
  error?: unknown;
}

interface ContentSuggestion {
  topic: string;
  keywords: string[];
  seasonality_score: number;
  predicted_engagement: number;
  ai_reasoning: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY not configured');
    }

    // Get historical blog performance data
    const { data: historicalPosts, error: historicalError } = await supabaseClient
      .from('blog_posts')
      .select('created_at, tags')
      .order('created_at', { ascending: false })
      .limit(50);

    if (historicalError) throw historicalError;

    // Analyze seasonal patterns
    const monthlyTags = historicalPosts?.reduce((acc, post) => {
      const month = format(parseISO(post.created_at), 'MM');
      acc[month] = acc[month] || [];
      acc[month].push(...(post.tags || []));
      return acc;
    }, {} as Record<string, string[]>);

    // Generate content suggestions for the next 3 months
    const suggestions: ContentSuggestion[] = [];
    for (let i = 1; i <= 3; i++) {
      const targetDate = addMonths(new Date(), i);
      const targetMonth = format(targetDate, 'MM');
      const historicalTags = monthlyTags?.[targetMonth] || [];

      const promptText = `
As a Nepal trekking and adventure travel expert, analyze historical content patterns and suggest blog topics for ${format(targetDate, 'MMMM yyyy')}.

Historical tags for this month: ${historicalTags.join(', ')}

Consider:
1. Seasonal trekking conditions in Nepal
2. Popular trekking routes during this time
3. Cultural events and festivals
4. Travel planning timeframes
5. Current travel trends

Provide content suggestions in this JSON format:
{
  "suggestions": [{
    "topic": "suggested blog title",
    "keywords": ["keyword1", "keyword2", ...],
    "seasonality_score": 0.0-1.0,
    "predicted_engagement": 0.0-1.0,
    "ai_reasoning": "explanation of why this content would perform well"
  }]
}`;

      const geminiResponse = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${geminiApiKey}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contents: [{
              parts: [{ text: promptText }]
            }],
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 1024,
            }
          })
        }
      );

      if (!geminiResponse.ok) {
        throw new Error(`Gemini API error: ${await geminiResponse.text()}`);
      }

      const geminiData = await geminiResponse.json() as GeminiResponse;
      const responseText = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!responseText) {
        throw new Error('No content received from Gemini');
      }

      try {
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No JSON found in response');
        }
        const parsedResponse = JSON.parse(jsonMatch[0]);
        suggestions.push(...(parsedResponse.suggestions || []));
      } catch (error) {
        console.error('Error parsing Gemini response:', error);
        continue;
      }
    }

    // Store suggestions in the content calendar
    for (const suggestion of suggestions) {
      const { error: insertError } = await supabaseClient
        .from('content_calendar')
        .insert({
          suggested_date: addMonths(new Date(), 1).toISOString(),
          content_type: 'blog',
          topic: suggestion.topic,
          keywords: suggestion.keywords,
          seasonality_score: suggestion.seasonality_score,
          predicted_engagement: suggestion.predicted_engagement,
          status: 'suggested',
          ai_reasoning: suggestion.ai_reasoning
        });

      if (insertError) {
        console.error('Error inserting suggestion:', insertError);
      }
    }

    return new Response(
      JSON.stringify({
        message: 'Content calendar updated',
        suggestions_count: suggestions.length
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Content calendar generation error:', error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
