/**
 * Google Gemini AI Service
 * Modern implementation using @google/genai SDK
 */

import { GoogleGenAI } from "@google/genai";

export interface GeminiConfig {
  apiKey: string;
  model?: string;
  temperature?: number;
  topK?: number;
  topP?: number;
  maxOutputTokens?: number;
}

export interface GeminiResponse {
  text: string;
  finishReason?: string;
  safetyRatings?: Array<{
    category: string;
    probability: string;
  }>;
}

export class GeminiService {
  private genAI: GoogleGenAI;
  private model: string;
  private defaultConfig: {
    temperature: number;
    topK: number;
    topP: number;
    maxOutputTokens: number;
  };

  constructor(config: GeminiConfig) {
    if (!config.apiKey) {
      throw new Error('Gemini API key is required');
    }

    this.genAI = new GoogleGenAI(config.apiKey);
    this.model = config.model || 'gemini-2.5-flash';
    this.defaultConfig = {
      temperature: config.temperature || 0.3,
      topK: config.topK || 20,
      topP: config.topP || 0.8,
      maxOutputTokens: config.maxOutputTokens || 1024
    };
  }

  async generateContent(
    prompt: string,
    customConfig?: Partial<GeminiConfig>
  ): Promise<GeminiResponse> {
    try {
      const model = this.genAI.getGenerativeModel({ 
        model: this.model,
        generationConfig: {
          temperature: customConfig?.temperature || this.defaultConfig.temperature,
          topK: customConfig?.topK || this.defaultConfig.topK,
          topP: customConfig?.topP || this.defaultConfig.topP,
          maxOutputTokens: customConfig?.maxOutputTokens || this.defaultConfig.maxOutputTokens,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
        ],
      });

      const result = await model.generateContent(prompt);
      const response = await result.response;
      
      if (!response) {
        throw new Error('No response received from Gemini API');
      }

      const text = response.text();
      
      if (!text) {
        throw new Error('No text content received from Gemini API');
      }

      return {
        text,
        finishReason: response.candidates?.[0]?.finishReason,
        safetyRatings: response.candidates?.[0]?.safetyRatings?.map(rating => ({
          category: rating.category,
          probability: rating.probability
        }))
      };
    } catch (error) {
      if (error instanceof Error) {
        // Enhanced error handling for common Gemini API issues
        if (error.message.includes('API_KEY_INVALID')) {
          throw new Error('Invalid Gemini API key. Please check your configuration.');
        }
        if (error.message.includes('QUOTA_EXCEEDED')) {
          throw new Error('Gemini API quota exceeded. Please check your usage limits.');
        }
        if (error.message.includes('RATE_LIMIT_EXCEEDED')) {
          throw new Error('Gemini API rate limit exceeded. Please try again later.');
        }
        if (error.message.includes('SAFETY')) {
          throw new Error('Content was blocked by Gemini safety filters.');
        }
        
        throw new Error(`Gemini API error: ${error.message}`);
      }
      
      throw new Error('Unknown error occurred while calling Gemini API');
    }
  }

  async generateSEOContent(
    content: string,
    title: string,
    excerpt?: string,
    tags?: string[],
    contentType: 'blog' | 'trek' | 'page' = 'blog'
  ): Promise<{
    optimizedTitle: string;
    optimizedExcerpt: string;
    optimizedContent: string;
    optimizedTags: string[];
  }> {
    const prompt = this.buildSEOOptimizationPrompt(content, title, excerpt, tags, contentType);
    
    const response = await this.generateContent(prompt, {
      temperature: 0.3, // Lower temperature for more consistent SEO output
      maxOutputTokens: 2048
    });

    return this.parseSEOResponse(response.text);
  }

  private buildSEOOptimizationPrompt(
    content: string,
    title: string,
    excerpt?: string,
    tags?: string[],
    contentType: 'blog' | 'trek' | 'page' = 'blog'
  ): string {
    const contentTypeContext = {
      blog: 'travel blog post about Nepal trekking and adventures',
      trek: 'trekking package description for Nepal adventures',
      page: 'informational page about Nepal travel and trekking'
    };

    return `You are an expert SEO content optimizer specializing in Nepal adventure tourism, trekking, and travel content.

CONTEXT: This is a ${contentTypeContext[contentType]} targeting international tourists from USA, UK, Australia, and Europe who are interested in adventurous travel, digital nomads, and eco-conscious tourism.

CURRENT CONTENT:
Title: ${title}
${excerpt ? `Excerpt: ${excerpt}` : ''}
${tags && tags.length > 0 ? `Current Tags: ${tags.join(', ')}` : ''}

Content:
${content}

OPTIMIZATION REQUIREMENTS:
1. Optimize for search engines while maintaining readability and authenticity
2. Target keywords: Nepal trekking, Himalayan adventures, eco-tourism, adventure travel
3. Include location-specific terms: Everest, Annapurna, Kathmandu, Pokhara
4. Appeal to adventure seekers, digital nomads, and eco-conscious travelers
5. Maintain the authentic voice and cultural sensitivity
6. Ensure content is engaging and informative

IMPORTANT: Return ONLY a valid JSON object with this exact structure:
{
  "optimizedTitle": "SEO-optimized title (max 60 characters)",
  "optimizedExcerpt": "SEO-optimized excerpt/meta description (max 160 characters)",
  "optimizedContent": "SEO-optimized content maintaining original structure and authenticity",
  "optimizedTags": ["tag1", "tag2", "tag3", "tag4", "tag5"]
}

Do not include any other text, explanations, or formatting outside the JSON object.`;
  }

  private parseSEOResponse(responseText: string): {
    optimizedTitle: string;
    optimizedExcerpt: string;
    optimizedContent: string;
    optimizedTags: string[];
  } {
    try {
      // Extract JSON from response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in Gemini response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Validate required fields
      if (!parsed.optimizedTitle || !parsed.optimizedExcerpt || !parsed.optimizedContent || !Array.isArray(parsed.optimizedTags)) {
        throw new Error('Invalid response format from Gemini API');
      }

      return {
        optimizedTitle: parsed.optimizedTitle,
        optimizedExcerpt: parsed.optimizedExcerpt,
        optimizedContent: parsed.optimizedContent,
        optimizedTags: parsed.optimizedTags
      };
    } catch (error) {
      throw new Error(`Failed to parse Gemini response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Health check method
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details: string }> {
    try {
      const response = await this.generateContent(
        'Respond with "OK" if you can process this request.',
        { maxOutputTokens: 10 }
      );
      
      if (response.text.includes('OK')) {
        return { status: 'healthy', details: 'Gemini API is responding correctly' };
      } else {
        return { status: 'unhealthy', details: 'Unexpected response from Gemini API' };
      }
    } catch (error) {
      return { 
        status: 'unhealthy', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

// Factory function for easy instantiation
export function createGeminiService(apiKey?: string): GeminiService {
  const key = apiKey || import.meta.env.VITE_GEMINI_API_KEY;
  
  if (!key) {
    throw new Error('Gemini API key not found. Please set VITE_GEMINI_API_KEY environment variable.');
  }

  return new GeminiService({
    apiKey: key,
    model: 'gemini-2.5-flash',
    temperature: 0.3,
    topK: 20,
    topP: 0.8,
    maxOutputTokens: 1024
  });
}
