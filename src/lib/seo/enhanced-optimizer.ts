/**
 * Enhanced SEO Optimizer with Circuit Breaker, Retry Logic, and Rate Limiting
 * Provides robust, production-ready SEO optimization capabilities
 */

import { CircuitBreaker, defaultSEOCircuitBreakerConfig, CircuitBreakerConfig, CircuitBreakerStats } from './circuit-breaker';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, defaultSEORetryConfig, RetryConfig, RetryStats } from './retry-handler';
import { RateLimiter, defaultSEORateLimitConfig, RateLimitConfig, RateLimitStats } from './rate-limiter';
// import { supabase } from '@/integrations/supabase/client'; // TODO: Add logging integration

export interface SEOOptimizationRequest {
  content: string;
  title: string;
  excerpt?: string;
  tags?: string[];
  targetKeywords?: string[];
  contentType: 'blog' | 'trek' | 'page';
  priority?: number; // 0-10, higher = more priority
}

export interface SEOOptimizationResponse {
  optimizedTitle: string;
  optimizedMetaDescription: string;
  focusKeywords: string[];
  contentSuggestions: ContentSuggestion[];
  technicalRecommendations: string[];
  optimizationConfidence: number;
  estimatedImpact: 'low' | 'medium' | 'high';
  processingTime: number;
  version: string;
}

export interface ContentSuggestion {
  section: string;
  currentText: string;
  suggestedText: string;
  reason: string;
  impact: 'low' | 'medium' | 'high';
}

export interface OptimizationStats {
  totalRequests: number;
  successfulOptimizations: number;
  failedOptimizations: number;
  averageProcessingTime: number;
  circuitBreakerStats: CircuitBreakerStats;
  retryStats: RetryStats;
  rateLimitStats: RateLimitStats;
}

export interface EnhancedOptimizerConfig {
  circuitBreaker?: CircuitBreakerConfig;
  retry?: RetryConfig;
  rateLimit?: RateLimitConfig;
  geminiApiKey?: string;
  enableFallback?: boolean;
  maxContentLength?: number;
  optimizationTimeout?: number;
}

export class EnhancedSEOOptimizer {
  private circuitBreaker: CircuitBreaker;
  private retryHandler: RetryHandler;
  private rateLimiter: RateLimiter;
  private stats: OptimizationStats;
  private geminiApiKey: string;

  constructor(config: EnhancedOptimizerConfig = {}) {
    this.circuitBreaker = new CircuitBreaker(
      config.circuitBreaker || defaultSEOCircuitBreakerConfig
    );
    this.retryHandler = new RetryHandler(
      config.retry || defaultSEORetryConfig
    );
    this.rateLimiter = new RateLimiter(
      config.rateLimit || defaultSEORateLimitConfig
    );

    this.geminiApiKey = config.geminiApiKey || import.meta.env.VITE_GEMINI_API_KEY;
    if (!this.geminiApiKey) {
      throw new Error('Gemini API key is required for SEO optimization');
    }

    this.stats = {
      totalRequests: 0,
      successfulOptimizations: 0,
      failedOptimizations: 0,
      averageProcessingTime: 0,
      circuitBreakerStats: this.circuitBreaker.getStats(),
      retryStats: this.retryHandler.getStats(),
      rateLimitStats: this.rateLimiter.getStats()
    };
  }

  async optimizeContent(request: SEOOptimizationRequest): Promise<SEOOptimizationResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // Validate input
      this.validateRequest(request);

      // Execute with rate limiting, circuit breaker, and retry logic
      const result = await this.rateLimiter.execute(async () => {
        return await this.circuitBreaker.execute(async () => {
          return await this.retryHandler.execute(async () => {
            return await this.performOptimization(request);
          }, `SEO optimization for ${request.contentType}`);
        });
      }, request.priority || 0);

      const processingTime = Date.now() - startTime;
      this.updateSuccessStats(processingTime);

      return {
        ...result,
        processingTime,
        version: '2.0.0'
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.updateFailureStats(processingTime);

      // Try fallback if enabled
      if (this.shouldUseFallback(error)) {
        console.warn('Using fallback optimization due to error:', error);
        return this.getFallbackOptimization(request, processingTime);
      }

      throw new Error(`SEO optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async performOptimization(request: SEOOptimizationRequest): Promise<Omit<SEOOptimizationResponse, 'processingTime' | 'version'>> {
    const prompt = this.buildOptimizationPrompt(request);

    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${this.geminiApiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Nepal-Adventure-Platform/1.0'
          },
          body: JSON.stringify({
            contents: [{
              parts: [{ text: prompt }]
            }],
            generationConfig: {
              temperature: 0.3, // Lower temperature for more consistent SEO output
              topK: 20,
              topP: 0.8,
              maxOutputTokens: 1024,
              candidateCount: 1
            },
            safetySettings: [
              {
                category: 'HARM_CATEGORY_HARASSMENT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE'
              },
              {
                category: 'HARM_CATEGORY_HATE_SPEECH',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE'
              }
            ]
          })
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `Gemini API error (${response.status})`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage += `: ${errorData.error?.message || errorText}`;
        } catch {
          errorMessage += `: ${errorText}`;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();

      // Enhanced error checking for Gemini API response
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No candidates returned from Gemini API');
      }

      const candidate = data.candidates[0];
      if (candidate.finishReason === 'SAFETY') {
        throw new Error('Content was blocked by safety filters');
      }

      const optimizedContent = candidate.content?.parts?.[0]?.text;
      if (!optimizedContent) {
        throw new Error('No content received from Gemini API response');
      }

      return this.parseOptimizationResponse(optimizedContent, request);
    } catch (error) {
      // Enhanced error handling with more specific error types
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to Gemini API');
      }

      if (error instanceof Error) {
        // Re-throw with additional context for debugging
        throw new Error(`SEO optimization failed: ${error.message}`);
      }

      throw new Error('Unknown error occurred during SEO optimization');
    }
  }

  private buildOptimizationPrompt(request: SEOOptimizationRequest): string {
    const { content, title, excerpt, tags, targetKeywords, contentType } = request;

    return `You are an expert SEO specialist for Nepal travel and adventure content. Your goal is to optimize content for travelers searching for Nepal trekking, adventure tours, and cultural experiences.

CURRENT CONTENT ANALYSIS:
- Title: ${title}
- Meta Description: ${excerpt || "Not provided"}
- Current Tags: ${tags?.join(', ') || 'Not provided'}
- Target Keywords: ${targetKeywords?.join(', ') || 'Not provided'}
- Content Type: ${contentType}

CONTENT TO OPTIMIZE:
${content.substring(0, 4000)}${content.length > 4000 ? '...' : ''}

SEO OPTIMIZATION REQUIREMENTS:
1. **Title Optimization**: Create a compelling title (50-60 characters) that includes primary keywords and appeals to adventure travelers
2. **Meta Description**: Write an engaging meta description (150-160 characters) that includes a call-to-action
3. **Keywords**: Suggest 3-5 high-value keywords focusing on Nepal travel, trekking, and adventure tourism
4. **Content Improvements**: Provide specific, actionable recommendations to improve SEO performance
5. **Technical SEO**: Consider search intent, semantic keywords, and user experience
6. **Confidence Rating**: Rate your optimization confidence (1-10) based on content quality and keyword potential

FOCUS AREAS FOR NEPAL TRAVEL CONTENT:
- Include location-specific keywords (e.g., "Nepal", "Himalayas", "Kathmandu")
- Target adventure and trekking terms (e.g., "trekking", "expedition", "adventure")
- Consider seasonal and difficulty-based keywords
- Include practical information keywords (e.g., "guide", "cost", "best time")
- Appeal to different traveler types (solo, family, experienced trekkers)

Return your response as a JSON object with this structure:
{
  "optimizedTitle": "string",
  "optimizedMetaDescription": "string",
  "focusKeywords": ["string"],
  "contentSuggestions": [{
    "section": "string",
    "currentText": "string",
    "suggestedText": "string",
    "reason": "string",
    "impact": "low|medium|high"
  }],
  "technicalRecommendations": ["string"],
  "optimizationConfidence": number,
  "estimatedImpact": "low|medium|high"
}`;
  }

  private parseOptimizationResponse(
    response: string,
    _request: SEOOptimizationRequest
  ): Omit<SEOOptimizationResponse, 'processingTime' | 'version'> {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      // Validate required fields
      if (!parsed.optimizedTitle || !parsed.optimizedMetaDescription) {
        throw new Error('Missing required optimization fields');
      }

      return {
        optimizedTitle: parsed.optimizedTitle,
        optimizedMetaDescription: parsed.optimizedMetaDescription,
        focusKeywords: parsed.focusKeywords || [],
        contentSuggestions: parsed.contentSuggestions || [],
        technicalRecommendations: parsed.technicalRecommendations || [],
        optimizationConfidence: parsed.optimizationConfidence || 5,
        estimatedImpact: parsed.estimatedImpact || 'medium'
      };
    } catch (error) {
      console.error('Failed to parse optimization response:', error);
      throw new Error(`Failed to parse optimization response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private validateRequest(request: SEOOptimizationRequest): void {
    if (!request.content || request.content.trim().length === 0) {
      throw new Error('Content is required for optimization');
    }

    if (!request.title || request.title.trim().length === 0) {
      throw new Error('Title is required for optimization');
    }

    if (request.content.length > 10000) {
      throw new Error('Content is too long for optimization (max 10,000 characters)');
    }
  }

  private shouldUseFallback(error: unknown): boolean {
    if (!(error instanceof Error)) return false;

    const fallbackTriggers = [
      'rate limit',
      'quota exceeded',
      'service unavailable',
      'timeout'
    ];

    return fallbackTriggers.some(trigger =>
      error.message.toLowerCase().includes(trigger)
    );
  }

  private getFallbackOptimization(
    request: SEOOptimizationRequest,
    processingTime: number
  ): SEOOptimizationResponse {
    // Basic fallback optimization using simple rules
    const optimizedTitle = this.generateFallbackTitle(request.title, request.tags);
    const optimizedMetaDescription = this.generateFallbackMetaDescription(request.excerpt, request.content);

    return {
      optimizedTitle,
      optimizedMetaDescription,
      focusKeywords: request.tags?.slice(0, 5) || [],
      contentSuggestions: [],
      technicalRecommendations: [
        'AI optimization temporarily unavailable - using basic optimization',
        'Consider manual review of title and meta description',
        'Retry optimization later for enhanced suggestions'
      ],
      optimizationConfidence: 3,
      estimatedImpact: 'low',
      processingTime,
      version: '2.0.0-fallback'
    };
  }

  private generateFallbackTitle(title: string, _tags?: string[]): string {
    if (title.length <= 60) {
      return title;
    }

    // Truncate and add ellipsis
    return title.substring(0, 57).trim() + '...';
  }

  private generateFallbackMetaDescription(excerpt?: string, content?: string): string {
    const source = excerpt || content?.substring(0, 200) || '';

    if (source.length <= 160) {
      return source;
    }

    return source.substring(0, 157).trim() + '...';
  }

  private updateSuccessStats(processingTime: number): void {
    this.stats.successfulOptimizations++;
    this.updateAverageProcessingTime(processingTime);
    this.updateComponentStats();
  }

  private updateFailureStats(processingTime: number): void {
    this.stats.failedOptimizations++;
    this.updateAverageProcessingTime(processingTime);
    this.updateComponentStats();
  }

  private updateAverageProcessingTime(processingTime: number): void {
    const totalTime = this.stats.averageProcessingTime * (this.stats.totalRequests - 1) + processingTime;
    this.stats.averageProcessingTime = totalTime / this.stats.totalRequests;
  }

  private updateComponentStats(): void {
    this.stats.circuitBreakerStats = this.circuitBreaker.getStats();
    this.stats.retryStats = this.retryHandler.getStats();
    this.stats.rateLimitStats = this.rateLimiter.getStats();
  }

  // Public methods for monitoring and management
  getStats(): OptimizationStats {
    this.updateComponentStats();
    return { ...this.stats };
  }

  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulOptimizations: 0,
      failedOptimizations: 0,
      averageProcessingTime: 0,
      circuitBreakerStats: this.circuitBreaker.getStats(),
      retryStats: this.retryHandler.getStats(),
      rateLimitStats: this.rateLimiter.getStats()
    };

    this.circuitBreaker.reset();
    this.retryHandler.reset();
  }

  destroy(): void {
    this.rateLimiter.destroy();
  }

  // Health check
  async healthCheck(): Promise<{ status: 'healthy' | 'degraded' | 'unhealthy'; details: Record<string, unknown> }> {
    const circuitStats = this.circuitBreaker.getStats();
    const rateLimitStats = this.rateLimiter.getStats();

    if (circuitStats.state === 'OPEN') {
      return {
        status: 'unhealthy',
        details: { reason: 'Circuit breaker is open', circuitStats, rateLimitStats }
      };
    }

    if (rateLimitStats.queueLength > rateLimitStats.queueLength * 0.8) {
      return {
        status: 'degraded',
        details: { reason: 'Rate limit queue is nearly full', circuitStats, rateLimitStats }
      };
    }

    return {
      status: 'healthy',
      details: { circuitStats, rateLimitStats }
    };
  }
}
